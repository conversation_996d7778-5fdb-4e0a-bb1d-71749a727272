from PySide6.QtCore import Qt, QSize, Signal, QTimer, QPoint
from PySide6.QtWidgets import (QDialog, QPushButton, QLabel, QVBoxLayout,
                               QHBoxLayout, QFrame, QWidget, QSlider,
                               QSpinBox, QButtonGroup, QApplication, QLineEdit, QAbstractSpinBox,
                               QMenu, QMessageBox)
from PySide6.QtGui import QPixmap, QPainter, QColor, QFont, QMouseEvent

from .alert_wiidget import AlertWidget, QuestionSaveWidget
from ..components.finger_control_widget import FingerControlWidget
from ..ui.gesture_edit import Ui_Dialog
from ...core import FingerID
from ...core.gesture_data_manager import StepData, GestureData
from ...utils.resource_helper import get_icon, get_image, get_image_path
from ..components.custom_slider import FingerSliderV

_STYLE = """
font-family: 'Alibaba PuHuiTi 2.0';
color: {};
background-color: {};
font-size: {};
font-weight: {};
border: {};
letter-spacing: 0px;  /* 可能不支持，依赖平台 */
"""

_BUTTON_STYLE = """
QPushButton {{
    font-family: 'Alibaba PuHuiTi 2.0';
    color: {};
    background-color: {};
    font-size: {};
    font-weight: {};
    border: {} solid #FF9429;
    border-radius: 8px;
}}
"""

# 最大允许添加的步数
MAX_STEPS = 31


class DurationInputDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.Tool | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.step_widget = None  # 引用父StepWidget
        self.setFixedSize(126, 36)
        self.setStyleSheet("QDialog {background-color: #3F4349; border: 1px; border-radius: 10px; padding: 0px;}")

    def set_step_widget(self, step_widget):
        self.step_widget = step_widget

    def keyPressEvent(self, arg__1):
        return None


class TimeBarWidget(QPushButton):
    def __init__(self, duration_ms=1200, parent=None):
        super().__init__(parent)
        self.duration_ms = duration_ms
        self.setFixedSize(5, 51)
        self.setToolTip(str(self.duration_ms) + "ms")
        self.setCursor(Qt.PointingHandCursor)

        self._setup_ui()

    def _setup_ui(self):
        self.setStyleSheet("""
            QPushButton {
                background-color: #FF9429;
                border: none;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #FFB520;
            }
        """)

    def set_duration(self, duration_ms):
        self.duration_ms = duration_ms
        self.setToolTip(str(self.duration_ms) + "ms")


class StepWidget(QFrame):
    clicked = Signal(int)  # step_index
    time_changed = Signal(int, int)  # step_index, new_time_ms
    delete_requested = Signal(int)  # step_index

    def __init__(self, step_data: StepData, parent=None):
        super().__init__(parent)
        self.step_index = step_data.index
        self.duration_ms = step_data.duration
        self.selected = False
        self._is_processing_duration_change = False  # 防止重复处理标志位

        self.change_duration_dialog = DurationInputDialog(self)
        self.change_duration_dialog.set_step_widget(self)
        self.change_duration_dialog.setFixedSize(120, 40)
        self.change_duration_dialog.hide()

        # 设置change_duration_widget的布局和样式
        layout = QHBoxLayout(self.change_duration_dialog)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 时间输入框
        self.change_duration_spinbox = QSpinBox()
        self.change_duration_spinbox.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.change_duration_spinbox.setRange(1, 20000)
        self.change_duration_spinbox.setSuffix(" ms")
        self.change_duration_spinbox.setValue(self.duration_ms)
        self.change_duration_spinbox.setFixedHeight(34)
        self.change_duration_spinbox.setStyleSheet("""
            QSpinBox {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: white;
                font-size: 18px;
            }
        """)
        self.symbol_label = QLabel()
        self.symbol_label.setText(">|<")
        self.symbol_label.setStyleSheet("background-color: transparent; color: white; font-size: 18px;border: none;")
        layout.addWidget(self.symbol_label)
        layout.addWidget(self.change_duration_spinbox)

        # 设置change_duration_widget的样式
        self.change_duration_dialog.setStyleSheet("""
            QWidget {
                background-color: #424348;
                border: 1px solid #666666;
                border-radius: 6px;
            }
        """)

        # 连接信号
        self.change_duration_spinbox.editingFinished.connect(self._on_duration_input_finished)

        self.setStyleSheet("QFrame {background-color: transparent;}")

        # 设置右键菜单策略
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)

        self.setFixedSize(118, 115)
        self._setup_ui()

    def _setup_ui(self):

        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 左侧图片区域
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setText(f"Step {self.step_index + 1}")
        self.image_label.setStyleSheet("background-color: #3F4349; border: none; color: white;")
        layout.addWidget(self.image_label)

        # 时间调整竖杠
        self.time_bar = TimeBarWidget(self.duration_ms)
        self.time_bar.clicked.connect(self._show_duration_dialog)

        layout.addWidget(self.time_bar)
        self.setLayout(layout)

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.step_index)
        super().mousePressEvent(event)

    def _show_duration_dialog(self):
        if self.change_duration_dialog.isVisible():
            print("Duration widget already visible, returning")
            return

        # 更新输入框的值
        self.change_duration_spinbox.setValue(self.duration_ms)

        # 计算显示位置（在StepWidget上方）
        global_pos = self.mapToGlobal(QPoint(0, 0))
        widget_width = self.change_duration_dialog.width()
        step_width = self.width()

        # 水平居中对齐
        x = global_pos.x() + (step_width) // 2
        # 垂直位置在StepWidget上方，留一些间距
        y = global_pos.y() - self.change_duration_dialog.height() - 10

        self.change_duration_dialog.move(x, y)
        self.change_duration_dialog.show()
        self.change_duration_dialog.raise_()  # 确保窗口在最前面
        self.change_duration_dialog.activateWindow()  # 激活窗口

        # 延迟设置焦点，确保窗口已经完全显示
        QTimer.singleShot(10, lambda: self.change_duration_spinbox.setFocus())
        QTimer.singleShot(20, lambda: self.change_duration_spinbox.selectAll())

    def _hide_duration_widget(self):
        self.change_duration_dialog.hide()

    def _on_duration_input_finished(self):
        # 防止重复处理
        if self._is_processing_duration_change:
            return

        self._is_processing_duration_change = True

        try:
            new_duration = self.change_duration_spinbox.value()
            if new_duration > 0:
                self.duration_ms = new_duration
                self.time_bar.set_duration(new_duration)
                self.time_changed.emit(self.step_index, new_duration)
        except ValueError:
            # 如果输入无效，恢复原值
            self.change_duration_spinbox.setValue(self.duration_ms)
        finally:
            # 延迟重置标志位，确保所有相关的信号处理完成
            QTimer.singleShot(50, lambda: setattr(self, '_is_processing_duration_change', False))

        self._hide_duration_widget()

    def set_selected(self, selected):
        self.selected = selected
        if selected:
            self.setStyleSheet("""
                QFrame {
                    background-color: transparent;
                    border: 1px solid #FF7700;
                    border-radius: 8px;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    background-color: transparent;
                    border: none;
                    border-radius: 8px;
                }
            """)

    def _show_context_menu(self, position):
        context_menu = QMenu(self)

        # 设置菜单样式
        context_menu.setStyleSheet("""
            QMenu {
                background-color: #3F4349;
                border: 1px solid #FF9429;
                border-radius: 4px;
                padding: 4px;
            }
            QMenu::item {
                background-color: transparent;
                color: white;
                padding: 6px 12px;
                border-radius: 2px;
                width: 100px;
                height: 30px;
            }
            QMenu::item:selected {
                background-color: #FF7700;
            }
        """)

        # 添加删除动作
        delete_action = context_menu.addAction(self.tr("Delete"))
        delete_action.triggered.connect(self._on_delete_requested)

        # 在鼠标位置显示菜单
        context_menu.exec(self.mapToGlobal(position))

    def _on_delete_requested(self):
        self.delete_requested.emit(self.step_index)


class AddStepWidget(QFrame):
    clicked = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self.setFixedSize(118, 115)

    def _setup_ui(self):
        self.setObjectName("AddGestureCard")
        self.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: 1px solid #FF7700;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        self.image_label = QLabel()
        self.image_label.setScaledContents(True)
        self._load_image(get_image_path("add_step.png", "gesture"))
        self.image_label.setStyleSheet("""QLabel {background-color: transparent; border: none;}""")
        layout.addWidget(self.image_label, 0, Qt.AlignCenter)

        self.setLayout(layout)

    def _load_image(self, img_path):
        pixmap = QPixmap(img_path)
        if not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                QSize(99, 118),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)

    def _setup_style(self):
        self.setStyleSheet("""
            #AddGestureCard {
                background-color: #333338;
                border-radius: 12px;
                border: 2px solid transparent;
            }
            #AddGestureCard:hover {
                background-color: #3D3D42;
                border: 2px solid #666666;
            }
            QLabel {
                color: #AAAAAA;
                font-size: 12px;
                font-weight: bold;
            }
        """)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class FingerParamWidget(QWidget):
    value_changed = Signal(str, int)  # finger_name, value

    def __init__(self, finger_name, color, default_value=0, parent=None):
        super().__init__(parent)
        self.finger_name = finger_name
        self.color = color
        self.default_value = default_value

        self._setup_ui()

    def _setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 手指名称标签
        name_label = QLabel(self.finger_name)
        name_label.setStyleSheet(f"""
            QLabel {{
                color: {self.color};
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 12px;
                font-weight: bold;
                text-align: center;
            }}
        """)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(name_label)

        # 数值显示
        self.value_label = QLabel(str(self.default_value))
        self.value_label.setStyleSheet("""
            QLabel {
                color: white;
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 16px;
                font-weight: bold;
                text-align: center;
                background-color: #424348;
                border-radius: 4px;
                padding: 4px;
                min-height: 20px;
            }
        """)
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.value_label)

        # 滑块
        self.slider = QSlider(Qt.Orientation.Vertical)
        self.slider.setRange(0, 100)
        self.slider.setValue(self.default_value)
        self.slider.setFixedHeight(150)
        self.slider.setStyleSheet(f"""
            QSlider::groove:vertical {{
                background: #3F4349;
                width: 8px;
                border-radius: 4px;
            }}
            QSlider::handle:vertical {{
                background: {self.color};
                border: 2px solid {self.color};
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -4px 0;
            }}
            QSlider::handle:vertical:hover {{
                background: {self.color};
                border: 2px solid white;
            }}
            QSlider::sub-page:vertical {{
                background: {self.color};
                border-radius: 4px;
            }}
        """)

        self.slider.valueChanged.connect(self._on_value_changed)
        layout.addWidget(self.slider, 0, Qt.AlignmentFlag.AlignCenter)

        self.setLayout(layout)

    def _on_value_changed(self, value):
        self.value_label.setText(str(value))
        self.value_changed.emit(self.finger_name, value)

    def get_value(self):
        return self.slider.value()

    def set_value(self, value):
        self.slider.setValue(value)


class GestureEditDialog(QDialog, Ui_Dialog):
    def __init__(self, name=None, gesture_data=None, all_gesture_names=None, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        # 设置默认值
        self.name = name or ""
        self.original_name = name or ""  # 保存原始名称用于重置
        self.all_gesture_names = all_gesture_names or []
        self.original_gesture_data = gesture_data  # 保存原始数据用于重置

        # 工作副本 - 所有修改都在这些副本上进行
        self.working_name = name or ""  # 工作中的名称
        self.working_steps = []  # 工作中的步骤数据

        self.current_step = 0  # 当前选中的step
        self.step_widgets = []  # 存储step组件
        self.finger_params = {}  # 存储手指参数控件
        self.data_modified = False  # 跟踪数据是否被修改

        self.resize(1594, 979)
        self.setWindowFlags(self.windowFlags() | Qt.FramelessWindowHint)

        self._init_ui()

        self.pushButton_save.setFocus()

    def set_input_range(self, step_range):
        for idx, control in enumerate([self.thumb_1_control, self.thumb_2_control, self.index_control,
                                       self.middle_control, self.ring_control, self.pinky_control]):
            control.doubleSpinBox_position.setRange(*step_range["positions"][idx])
            control.doubleSpinBox_speed.setRange(*step_range["speeds"][idx])
            control.doubleSpinBox_force.setRange(*step_range["force"][idx])
            control.slider.slider_container.setRange(*step_range["positions"][idx])

    def load_gesture_data(self):
        if self.original_gesture_data and len(self.original_gesture_data.steps):
            # 从传入的数据加载步骤到工作副本
            for step_data in self.original_gesture_data.steps:
                # 创建步骤数据的深拷贝
                working_step = StepData(
                    step_data.index,
                    step_data.duration,
                    step_data.mode,
                    step_data.positions.copy(),
                    step_data.speeds.copy(),
                    step_data.currents.copy()
                )
                self.working_steps.append(working_step)
                self._create_step_widget_from_data(working_step)
        else:
            # 如果没有数据，创建一个默认步骤（不标记为已修改）
            # self.add_step(mark_modified=False)
            pass

        # 选中第一个步骤
        if self.working_steps:
            self._select_step(0)
            # 滚动到底部
            self._scroll_to_bottom()

    def _create_step_widget_from_data(self, step_data):
        step_index = len(self.step_widgets)

        # 创建step组件
        step_widget = StepWidget(step_data)
        step_widget.clicked.connect(self._on_step_selected)
        step_widget.time_changed.connect(self._on_step_time_changed)
        step_widget.delete_requested.connect(self._on_step_delete_requested)
        self.step_widgets.append(step_widget)

        # 计算网格位置 (每行5个)
        row = step_index // 6
        col = step_index % 6

        # 添加到网格布局
        self.gridLayout_step.addWidget(step_widget, row, col)

        # 更新添加按钮位置
        self._update_add_step_position()

    def mousePressEvent(self, event):
        # 检查点击是否在任何StepWidget内
        clicked_on_step = False
        for step_widget in self.step_widgets:
            if step_widget.geometry().contains(event.pos()):
                clicked_on_step = True
                break

        # 如果没有点击在StepWidget上，隐藏所有打开的duration widgets
        if not clicked_on_step:
            for step_widget in self.step_widgets:
                if step_widget.change_duration_dialog.isVisible():
                    step_widget._hide_duration_widget()

        super().mousePressEvent(event)

    def _init_ui(self):
        # 设置主界面样式
        self.setStyleSheet("QDialog {background-color: #292E34;}")

        # 名称编辑
        self.modified_label = QLabel()
        self.modified_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.modified_label.setFixedSize(20, 20)
        self.modified_label.setStyleSheet("font-size: 18px;color: white;")
        self.horizontalLayout_2.insertWidget(0, self.modified_label)
        self.lineEdit_name.setText(self.working_name)
        self.lineEdit_name.setToolTip(self.working_name)
        self.lineEdit_name.setMaxLength(18)
        self.lineEdit_name.setStyleSheet(_STYLE.format("white", "transparent", "24px", 300, "none"))
        self.lineEdit_name.setReadOnly(True)
        self.pushButton_name_edit.setStyleSheet("border: none;")
        self.pushButton_name_edit.setFixedSize(22, 22)
        self.pushButton_name_edit.setIcon(get_icon("edit.svg"))
        self.pushButton_name_edit.setIconSize(QSize(20, 20))
        self.pushButton_name_edit.clicked.connect(self._on_name_edit_clicked)
        self.lineEdit_name.editingFinished.connect(self._on_name_editing_finished)

        # 退出按钮
        self.pushButton_close.setStyleSheet("border: none;")
        self.pushButton_close.setCursor(Qt.CursorShape.PointingHandCursor)
        self.pushButton_close.setFixedSize(22, 22)
        self.pushButton_close.setIcon(get_image("window_close.svg"))
        self.pushButton_close.setIconSize(QSize(20, 20))
        self.pushButton_close.clicked.connect(self._on_close_button_clicked)

        # hand urdf
        self.label_urdf.setFixedSize(733, 301)
        self.label_urdf.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_urdf.setPixmap(get_image("revo2_hand.png"))
        self.label_urdf.setStyleSheet("background-color: #3F4349; border-radius: 8px;")

        # 运行按钮
        self.pushButton_run.setFixedSize(35, 35)
        self.pushButton_run.setIcon(get_icon("run.png"))
        self.pushButton_run.setIconSize(QSize(34, 34))
        self.pushButton_run.setStyleSheet("border: none;")
        self.pushButton_run.setCursor(Qt.CursorShape.PointingHandCursor)

        # 初始化step区域
        self._init_step_area()

        # 初始化手指参数区域
        self._init_finger_params()

        # 初始化底部按钮
        self._init_bottom_buttons()

    def _on_name_edit_clicked(self):
        self.lineEdit_name.setReadOnly(False)
        self.lineEdit_name.setFocus()
        self.lineEdit_name.selectAll()

    def _on_close_button_clicked(self):
        self._try_close()

    def closeEvent(self, event):
        if self._try_close():
            event.accept()
        else:
            event.ignore()

    def _try_close(self):
        if self.data_modified:
            window = QuestionSaveWidget(self)
            title = self.tr("Done Editing")
            message = self.tr("The current gesture changes have not been saved. Are you sure you want to exit edit mode?")
            save_button_text = self.tr("Save")
            exit_button_text = self.tr("Exit Without Saving")
            img = get_image("alert.svg")
            window.show_alert(title, message, save_button_text, exit_button_text, img)

            if window.need_save:
                # 选择保存：先保存再关闭
                self._on_save_clicked()
                self.accept()
                return True
            else:
                # 选择不保存直接退出
                self.reject()
                return True
        else:
            # 没有修改，直接关闭
            self.reject()
            return True

    def _set_name_readonly(self):
        self.lineEdit_name.setReadOnly(True)
        self.lineEdit_name.clearFocus()

    def _on_name_editing_finished(self):
        current_name = self.lineEdit_name.text().strip()

        # 检查名称是否为空
        if not current_name:
            # 名称为空，重置为工作名称
            self.lineEdit_name.setText(self.working_name)
            current_name = self.working_name

        # 检查名称是否与其他手势重复（排除自己的原始名称）
        if current_name != self.original_name and current_name in self.all_gesture_names:
            # 显示警告
            alert = AlertWidget(self)
            title = self.tr("Name Conflict")
            message = self.tr("A gesture with this name already exists. Please choose a different name.")
            alert.set_image(get_image("alert.svg"))
            alert.show_alert(title, message, self.tr("OK"))

            # 重置为工作名称
            self.lineEdit_name.setText(self.working_name)
            current_name = self.working_name

        else:
            # 更新工作名称
            if current_name != self.working_name:
                self.working_name = current_name
                self._mark_data_modified()

        # 更新tooltip和设置只读
        self.lineEdit_name.setToolTip(current_name)
        QTimer.singleShot(50, self._set_name_readonly)

    def _init_step_area(self):
        # 设置滚动区域样式
        self.scrollArea_step.setFixedWidth(733)
        self.scrollArea_step.setWidgetResizable(True)
        self.scrollArea_step.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scrollArea_step.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scrollArea_step.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                padding: 0;
                border: none;
                margin: 0;
            }
            QScrollArea QWidget {
                background-color: transparent;
                padding: 0;
                margin: 0;
            }
            
            QScrollBar:vertical {
                background-color: #333333;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #FF9429;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #FF9429;
            }
            
        """)

        # 创建初始的添加按钮
        self._update_add_step_position()

    def _scroll_to_bottom(self):
        # 使用QTimer延迟执行，确保布局更新完成后再滚动
        QTimer.singleShot(30, lambda: self.scrollArea_step.verticalScrollBar().setValue(
            self.scrollArea_step.verticalScrollBar().maximum()
        ))

    def add_step(self, mark_modified=True):
        step_index = len(self.working_steps)
        if step_index < MAX_STEPS:
            mode = 2  # 默认是位置速度模式

            # 添加帧时默认速度为最大速度
            speed_values = [int(control.doubleSpinBox_speed.maximum()) for control in [self.thumb_1_control,
                                                                                  self.thumb_2_control,
                                                                                  self.index_control,
                                                                                  self.middle_control,
                                                                                  self.ring_control,
                                                                                  self.pinky_control]]

            step_data = StepData(step_index, 1200, mode, [0] * 6, speed_values, [0] * 6)
            self.working_steps.append(step_data)

            # 创建step组件
            step_widget = StepWidget(step_data)
            step_widget.clicked.connect(self._on_step_selected)
            step_widget.time_changed.connect(self._on_step_time_changed)
            step_widget.delete_requested.connect(self._on_step_delete_requested)
            self.step_widgets.append(step_widget)

            # 计算网格位置 (每行3个)
            row = step_index // 6
            col = step_index % 6

            # 添加到网格布局
            self.gridLayout_step.addWidget(step_widget, row, col)

            # 更新添加按钮位置
            self._update_add_step_position()

            # 选中新添加的step
            self._select_step(step_index)

            # 滚动到底部
            self._scroll_to_bottom()

            # 标记数据已修改（只有在用户主动添加时才标记）
            if mark_modified:
                self._mark_data_modified()

    def _update_add_step_position(self):
        # 先从布局中移除添加按钮
        if hasattr(self, 'add_step_widget'):
            self.gridLayout_step.removeWidget(self.add_step_widget)
        else:
            # 第一次创建添加按钮
            self.add_step_widget = AddStepWidget()
            self.add_step_widget.clicked.connect(self.add_step)

        # 计算添加按钮应该放置的位置
        step_count = len(self.working_steps)
        max_cols = 6
        row = step_count // max_cols
        col = step_count % max_cols

        # 将添加按钮放在新位置
        self.gridLayout_step.addWidget(self.add_step_widget, row, col)

    def _init_finger_params(self):
        self.frame_params.setStyleSheet("QFrame {background-color: #3F4349; border: 1px;}")

        self.thumb_1_control = FingerControlWidget(FingerID.Thumb, "Thumb", "Fl/Ex", "#78BB07", "right", spx_color="#FFFFFF", bg_color="#45494F")
        self.thumb_2_control = FingerControlWidget(FingerID.ThumbAux, "Thumb", "Ad/Ab", "#4454C6", "bottom", spx_color="#FFFFFF", bg_color="#45494F")
        self.index_control = FingerControlWidget(FingerID.Index, "Index", "Fl/Ex", "#F0A501", "right", spx_color="#FFFFFF", bg_color="#45494F")
        self.middle_control = FingerControlWidget(FingerID.Middle, "Middle", "Fl/Ex", "#E63D45", "right", spx_color="#FFFFFF", bg_color="#45494F")
        self.ring_control = FingerControlWidget(FingerID.Ring, "Ring", "Fl/Ex", "#5098BD", "right", spx_color="#FFFFFF", bg_color="#45494F")
        self.pinky_control = FingerControlWidget(FingerID.Pinky, "Pinky", "Fl/Ex", "#008545", "right", spx_color="#FFFFFF", bg_color="#45494F")
        for control in [self.thumb_1_control, self.thumb_2_control, self.index_control, self.middle_control, self.ring_control, self.pinky_control]:
            control.label_2.hide()
            control.doubleSpinBox_force.hide()

        self.gridLayout_params.addWidget(self.thumb_2_control, 0, 0)
        self.gridLayout_params.addWidget(self.thumb_1_control, 0, 1)
        self.gridLayout_params.addWidget(self.index_control, 0, 2)
        self.gridLayout_params.addWidget(self.middle_control, 1, 0)
        self.gridLayout_params.addWidget(self.ring_control, 1, 1)
        self.gridLayout_params.addWidget(self.pinky_control, 1, 2)

        # 连接手指控件的值变化信号
        for control in [self.thumb_1_control, self.thumb_2_control, self.index_control, self.middle_control, self.ring_control, self.pinky_control]:
            # control.slider_value_changed_signal.connect(self._on_finger_position_changed)
            control.value_changed_signal.connect(self._on_finger_value_changed)

    def _init_bottom_buttons(self):
        button_configs = [
            ("Reset", "transparent", "#FF9429"),
            ("Save", "#FF9429", "black")
        ]

        buttons = [self.pushButton_reset, self.pushButton_save]

        for i, (text, bg_color, text_color) in enumerate(button_configs):
            button = buttons[i]
            button.setText(text)
            button.setFixedSize(320, 60)
            button.setStyleSheet(_BUTTON_STYLE.format(
                text_color, bg_color, "20px", "300", "1px"
            ))

        # 连接信号
        self.pushButton_reset.clicked.connect(self._on_reset_clicked)
        self.pushButton_save.clicked.connect(lambda : self._on_save_clicked(True))

    def _on_step_selected(self, step_index):
        self._select_step(step_index)

    def _select_step(self, step_index):
        if 0 <= step_index < len(self.working_steps):
            # 更新当前step
            self.current_step = step_index

            # 更新组件选中状态
            for i, widget in enumerate(self.step_widgets):
                widget.set_selected(i == step_index)

            # 更新手指参数显示
            step_data = self.working_steps[step_index]

            # 更新各个手指控件的值
            for idx, control in enumerate([self.thumb_1_control, self.thumb_2_control, self.index_control,
                                           self.middle_control, self.ring_control, self.pinky_control]):
                control.set_value(step_data.positions[idx], step_data.speeds[idx], step_data.currents[idx])

    def _on_step_time_changed(self, step_index, new_time_ms):
        if 0 <= step_index < len(self.working_steps):
            old_time = self.working_steps[step_index].duration
            if old_time != new_time_ms:
                self.working_steps[step_index].duration = new_time_ms
                self._mark_data_modified()

    def _on_finger_position_changed(self, finger_id, value):
        if 0 <= self.current_step < len(self.working_steps):
            old_value = self.working_steps[self.current_step].positions[finger_id.value - 1]
            if old_value != value:
                self.working_steps[self.current_step].positions[finger_id.value - 1] = value
                self._mark_data_modified()

    def _on_finger_value_changed(self, finger_id, value_type, value):
        if not (0 <= self.current_step < len(self.working_steps)):
            return

        type_map = {
            "position": "positions",
            "speed": "speeds",
            "current": "currents"
        }

        attr_name = type_map.get(value_type, "currents")  # 默认为 currents
        step = self.working_steps[self.current_step]
        index = finger_id.value - 1

        values_list = getattr(step, attr_name)
        if values_list[index] != value:
            values_list[index] = value
            self._mark_data_modified()

    def _mark_data_modified(self):
        self.data_modified = True
        self.modified_label.setText("*")

    def _on_reset_clicked(self):
        # # 弹出确认对话框
        # reply = QMessageBox.question(
        #     self,
        #     "Reset Gesture",
        #     "Are you sure you want to reset all changes to the original state?",
        #     QMessageBox.Yes | QMessageBox.No,
        #     QMessageBox.No
        # )
        #
        # if reply == QMessageBox.Yes:
        # 重置数据
        self._reset_to_original_data()

        # 重置修改状态
        self.data_modified = False

        # 更新窗口标题
        self.modified_label.setText("")

    def _reset_to_original_data(self):
        # 重置工作名称
        self.working_name = self.original_name
        self.lineEdit_name.setText(self.working_name)
        self.lineEdit_name.setToolTip(self.working_name)

        # 清空现有步骤
        self.working_steps.clear()

        # 清空现有组件
        for widget in self.step_widgets:
            self.gridLayout_step.removeWidget(widget)
            widget.deleteLater()

        if hasattr(self, 'add_step_widget'):
            self.gridLayout_step.removeWidget(self.add_step_widget)

        self.step_widgets.clear()

        # 重新加载原始数据
        if self.original_gesture_data and len(self.original_gesture_data.steps):
            # 从原始数据重新加载步骤到工作副本
            for step_data in self.original_gesture_data.steps:
                # 创建步骤数据的深拷贝
                working_step = StepData(
                    step_data.index,
                    step_data.duration,
                    step_data.mode,
                    step_data.positions.copy(),
                    step_data.speeds.copy(),
                    step_data.currents.copy()
                )
                self.working_steps.append(working_step)
                self._create_step_widget_from_data(working_step)
        else:
            # 如果没有原始数据，创建一个默认步骤（不标记为已修改）
            # self.add_step(mark_modified=False)
            pass

        # 更新添加步骤按钮的位置
        self._update_add_step_position()

        # 选中第一个步骤
        if self.working_steps:
            self._select_step(0)
            # 滚动到底部
            self._scroll_to_bottom()

    def _on_delete_clicked(self):
        if len(self.working_steps) > 1 and 0 <= self.current_step < len(self.working_steps):
            # 删除当前step
            del self.working_steps[self.current_step]

            # 重新构建step组件
            self._rebuild_step_widgets()

            # 选中前一个step（如果当前是最后一个，则选中新的最后一个）
            new_current = min(self.current_step, len(self.working_steps) - 1)
            self._select_step(new_current)

    def _on_step_delete_requested(self, step_index):
        if len(self.working_steps) > 1 and 0 <= step_index < len(self.working_steps):
            # 删除指定的step
            del self.working_steps[step_index]

            # 重新构建step组件
            self._rebuild_step_widgets()

            # 选择合适的step
            if step_index == self.current_step:
                # 如果删除的是当前选中的step，选中前一个step（如果当前是最后一个，则选中新的最后一个）
                new_current = min(step_index, len(self.working_steps) - 1)
                self._select_step(new_current)
            elif step_index < self.current_step:
                # 如果删除的step在当前选中step之前，需要调整当前选中的索引
                self.current_step -= 1
                self._select_step(self.current_step)
            # 如果删除的step在当前选中step之后，不需要调整当前选中的索引

            # 标记数据已修改
            self._mark_data_modified()

    def _rebuild_step_widgets(self):
        # 清空现有组件
        for widget in self.step_widgets:
            self.gridLayout_step.removeWidget(widget)
            widget.deleteLater()

        if hasattr(self, 'add_step_widget'):
            self.gridLayout_step.removeWidget(self.add_step_widget)

        self.step_widgets.clear()

        # 重新创建组件并更新索引
        for i, step_data in enumerate(self.working_steps):
            step_data.index = i  # 更新索引
            step_widget = StepWidget(step_data)
            step_widget.clicked.connect(self._on_step_selected)
            step_widget.time_changed.connect(self._on_step_time_changed)
            step_widget.delete_requested.connect(self._on_step_delete_requested)
            self.step_widgets.append(step_widget)

            # 计算网格位置
            row = i // 6
            col = i % 6
            self.gridLayout_step.addWidget(step_widget, row, col)

        # 更新添加按钮位置
        self._update_add_step_position()

    def _on_save_clicked(self, on_save_button=False):

        if not self.data_modified:
            self.reject()
            return

        # 获取手势名称
        gesture_name = self.working_name.strip()
        if not gesture_name:
            QMessageBox.warning(self, self.tr("Warning"), self.tr("Please enter a gesture name!"))
            return

        # 验证步骤数据
        if not self.working_steps:
            QMessageBox.warning(self, self.tr("Warning"), self.tr("Please add at least one step!"))
            return

        # 构建手势数据 - 只有在保存时才真正更新原始数据
        gesture_data = GestureData(self.original_gesture_data.action_id, gesture_name, True)
        gesture_data.steps = self.working_steps

        try:
            # 保存成功，更新原始数据为当前工作数据
            self.original_gesture_data = gesture_data
            self.original_name = gesture_name

            # 重置修改状态
            self.data_modified = False

            # 更新窗口标题
            self.modified_label.setText("")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save gesture: {str(e)}")

    def get_gesture_data(self):
        return self.original_gesture_data

    def update_gesture_data(self, name, gesture_data, all_gesture_names):
        """更新对话框的手势数据，用于重用对话框实例"""
        # 更新基本信息
        self.name = name
        self.original_name = name
        self.all_gesture_names = all_gesture_names
        self.original_gesture_data = gesture_data

        # 重置工作副本
        self.working_name = name
        self.working_steps = []

        # 重置修改状态
        self.data_modified = False
        self.modified_label.setText("")

        # 更新UI显示
        self.lineEdit_name.setText(self.working_name)
        self.lineEdit_name.setToolTip(self.working_name)

        # 清空现有步骤组件
        for widget in self.step_widgets:
            self.gridLayout_step.removeWidget(widget)
            widget.deleteLater()

        if hasattr(self, 'add_step_widget'):
            self.gridLayout_step.removeWidget(self.add_step_widget)

        self.step_widgets.clear()
        self.current_step = 0

        # 更新添加步骤按钮的位置
        self._update_add_step_position()

